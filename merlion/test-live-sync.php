<?php
/**
 * Live Sync Testing Tool
 * This tool helps you test real-time sync from <PERSON>pp<PERSON> to your database
 */

session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

require_once '../functions/appika_reverse_sync.php';

$message = '';
$message_type = '';
$before_data = null;
$after_data = null;
$sync_result = null;

// Handle sync test
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_sync'])) {
    $ticket_id = intval($_POST['ticket_id']);
    
    if ($ticket_id > 0) {
        // Get ticket data BEFORE sync
        $before_query = "SELECT id, status, priority, updated_at, appika_updated_at FROM support_tickets WHERE id = ?";
        $stmt = mysqli_prepare($conn, $before_query);
        mysqli_stmt_bind_param($stmt, 'i', $ticket_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $before_data = mysqli_fetch_assoc($result);
        
        if ($before_data) {
            // Perform sync
            $sync_result = syncFromAppika($ticket_id);
            
            // Get ticket data AFTER sync
            $stmt = mysqli_prepare($conn, $before_query);
            mysqli_stmt_bind_param($stmt, 'i', $ticket_id);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $after_data = mysqli_fetch_assoc($result);
            
            $message = 'Sync test completed!';
            $message_type = 'info';
        } else {
            $message = 'Ticket not found!';
            $message_type = 'error';
        }
    } else {
        $message = 'Please enter a valid ticket ID.';
        $message_type = 'warning';
    }
}

// Get tickets with Appika IDs for testing
$query = "SELECT id, subject, appika_id, status, priority, updated_at 
          FROM support_tickets 
          WHERE appika_id IS NOT NULL AND appika_id != '' 
          ORDER BY updated_at DESC 
          LIMIT 10";
$tickets_result = mysqli_query($conn, $query);
$test_tickets = [];
while ($row = mysqli_fetch_assoc($tickets_result)) {
    $test_tickets[] = $row;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Sync Testing</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <link rel="stylesheet" href="../css/main.css">
    
    <style>
    .test-container {
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .test-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        padding: 20px;
    }
    
    .comparison-table {
        width: 100%;
        border-collapse: collapse;
        margin: 15px 0;
    }
    
    .comparison-table th,
    .comparison-table td {
        border: 1px solid #ddd;
        padding: 12px;
        text-align: left;
    }
    
    .comparison-table th {
        background-color: #f8f9fa;
        font-weight: bold;
    }
    
    .changed {
        background-color: #fff3cd !important;
        font-weight: bold;
    }
    
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    
    .step-box {
        background: #e8f4fd;
        border-left: 4px solid #007bff;
        padding: 15px;
        margin: 15px 0;
    }
    
    .ticket-item {
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        margin: 5px 0;
        cursor: pointer;
        transition: background-color 0.3s;
    }
    
    .ticket-item:hover {
        background-color: #f8f9fa;
    }
    </style>
</head>

<body>
    <div class="test-container">
        <div class="test-card">
            <h2><i class="fas fa-sync-alt text-primary"></i> Live Sync Testing Tool</h2>
            <p class="text-muted">Test if changes in Appika automatically sync to your database</p>
            
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type === 'error' ? 'danger' : ($message_type === 'warning' ? 'warning' : 'info'); ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Instructions -->
        <div class="test-card">
            <h4><i class="fas fa-info-circle"></i> How to Test Live Sync</h4>
            
            <div class="step-box">
                <strong>Step 1:</strong> Choose a ticket from the list below and note its current status/priority
            </div>
            
            <div class="step-box">
                <strong>Step 2:</strong> Go to your Appika system and change that ticket's status or priority
            </div>
            
            <div class="step-box">
                <strong>Step 3:</strong> Come back here and click "Test Sync" for that ticket
            </div>
            
            <div class="step-box">
                <strong>Step 4:</strong> Check the "Before vs After" comparison to see if changes were synced
            </div>
        </div>

        <!-- Test Form -->
        <div class="test-card">
            <h4>Sync Test</h4>
            
            <form method="POST">
                <div class="row">
                    <div class="col-md-6">
                        <label for="ticket_id">Ticket ID to Test:</label>
                        <input type="number" class="form-control" name="ticket_id" id="ticket_id" required min="1" 
                               value="<?php echo isset($_POST['ticket_id']) ? $_POST['ticket_id'] : ''; ?>">
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <button type="submit" name="test_sync" class="btn btn-primary">
                            <i class="fas fa-sync"></i> Test Sync Now
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Available Test Tickets -->
        <?php if (!empty($test_tickets)): ?>
        <div class="test-card">
            <h4>Available Test Tickets</h4>
            <p class="text-muted">Click on a ticket to select it for testing:</p>
            
            <?php foreach ($test_tickets as $ticket): ?>
                <div class="ticket-item" onclick="document.getElementById('ticket_id').value = <?php echo $ticket['id']; ?>">
                    <strong>Ticket #<?php echo $ticket['id']; ?></strong> 
                    <span class="badge badge-info"><?php echo htmlspecialchars($ticket['appika_id']); ?></span>
                    <br>
                    <small><?php echo htmlspecialchars($ticket['subject']); ?></small>
                    <br>
                    <small class="text-muted">
                        Current Status: <strong><?php echo ucfirst($ticket['status']); ?></strong> | 
                        Priority: <strong><?php echo ucfirst($ticket['priority']); ?></strong> | 
                        Updated: <?php echo $ticket['updated_at']; ?>
                    </small>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <!-- Sync Results -->
        <?php if ($sync_result && $before_data && $after_data): ?>
        <div class="test-card">
            <h4>Sync Results</h4>
            
            <!-- Sync Status -->
            <div class="mb-3">
                <strong>Sync Status:</strong> 
                <span class="<?php echo $sync_result['success'] ? 'success' : 'error'; ?>">
                    <?php echo $sync_result['success'] ? '✅ Success' : '❌ Failed'; ?>
                </span>
                <br>
                <strong>Message:</strong> <?php echo htmlspecialchars($sync_result['message']); ?>
                <br>
                <strong>Updated:</strong> 
                <span class="<?php echo $sync_result['updated'] ? 'success' : 'warning'; ?>">
                    <?php echo $sync_result['updated'] ? '✅ Yes' : '⚠️ No'; ?>
                </span>
                <?php if ($sync_result['updated'] && !empty($sync_result['changes'])): ?>
                    <br><strong>Changes:</strong> <?php echo implode(', ', array_keys($sync_result['changes'])); ?>
                <?php endif; ?>
            </div>

            <!-- Before vs After Comparison -->
            <h5>Before vs After Comparison</h5>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Field</th>
                        <th>Before Sync</th>
                        <th>After Sync</th>
                        <th>Changed?</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="<?php echo $before_data['status'] !== $after_data['status'] ? 'changed' : ''; ?>">
                        <td><strong>Status</strong></td>
                        <td><?php echo ucfirst($before_data['status']); ?></td>
                        <td><?php echo ucfirst($after_data['status']); ?></td>
                        <td><?php echo $before_data['status'] !== $after_data['status'] ? '✅ Yes' : '❌ No'; ?></td>
                    </tr>
                    <tr class="<?php echo $before_data['priority'] !== $after_data['priority'] ? 'changed' : ''; ?>">
                        <td><strong>Priority</strong></td>
                        <td><?php echo ucfirst($before_data['priority']); ?></td>
                        <td><?php echo ucfirst($after_data['priority']); ?></td>
                        <td><?php echo $before_data['priority'] !== $after_data['priority'] ? '✅ Yes' : '❌ No'; ?></td>
                    </tr>
                    <tr class="<?php echo $before_data['updated_at'] !== $after_data['updated_at'] ? 'changed' : ''; ?>">
                        <td><strong>Updated At</strong></td>
                        <td><?php echo $before_data['updated_at']; ?></td>
                        <td><?php echo $after_data['updated_at']; ?></td>
                        <td><?php echo $before_data['updated_at'] !== $after_data['updated_at'] ? '✅ Yes' : '❌ No'; ?></td>
                    </tr>
                    <tr class="<?php echo $before_data['appika_updated_at'] !== $after_data['appika_updated_at'] ? 'changed' : ''; ?>">
                        <td><strong>Appika Updated At</strong></td>
                        <td><?php echo $before_data['appika_updated_at'] ?: 'Never'; ?></td>
                        <td><?php echo $after_data['appika_updated_at'] ?: 'Never'; ?></td>
                        <td><?php echo $before_data['appika_updated_at'] !== $after_data['appika_updated_at'] ? '✅ Yes' : '❌ No'; ?></td>
                    </tr>
                </tbody>
            </table>

            <?php if ($sync_result['updated']): ?>
                <div class="alert alert-success">
                    <strong>🎉 Success!</strong> Your ticket was updated from Appika. The changes are highlighted in yellow above.
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <strong>ℹ️ No Changes</strong> Either the ticket wasn't updated in Appika, or the values are already the same.
                </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- Quick Links -->
        <div class="test-card">
            <h4>Quick Links</h4>
            <a href="appika-updates-log.php" class="btn btn-outline-primary mr-2">
                <i class="fas fa-list"></i> View Updates Log
            </a>
            <a href="admin-tickets.php" class="btn btn-outline-secondary mr-2">
                <i class="fas fa-arrow-left"></i> Back to Tickets
            </a>
            <a href="diagnose-reverse-sync.php" class="btn btn-outline-info">
                <i class="fas fa-stethoscope"></i> Diagnose Setup
            </a>
        </div>
    </div>

    <script src="../js/vendor.min.js"></script>
</body>
</html>
