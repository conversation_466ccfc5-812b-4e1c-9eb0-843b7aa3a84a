<?php
/**
 * Simulate Appika Updates Tool
 * This tool simulates what happens when a ticket is updated in Appika
 * by directly calling the Appika API to update a ticket, then testing reverse sync
 */

session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

require_once '../functions/appika_reverse_sync.php';
require_once '../functions/graphql_functions.php';

$message = '';
$message_type = '';
$simulation_result = null;
$before_data = null;
$after_data = null;

// Handle simulation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['simulate'])) {
    $ticket_id = intval($_POST['ticket_id']);
    $new_status = $_POST['new_status'];
    $new_priority = $_POST['new_priority'];
    
    if ($ticket_id > 0) {
        // Get ticket data BEFORE simulation
        $before_query = "SELECT id, appika_id, status, priority, updated_at, appika_updated_at FROM support_tickets WHERE id = ?";
        $stmt = mysqli_prepare($conn, $before_query);
        mysqli_stmt_bind_param($stmt, 'i', $ticket_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $before_data = mysqli_fetch_assoc($result);
        
        if ($before_data && !empty($before_data['appika_id'])) {
            $simulation_result = [
                'steps' => [],
                'success' => false,
                'sync_result' => null
            ];
            
            // Step 1: Update ticket in Appika (simulate what admin would do)
            $simulation_result['steps'][] = "Step 1: Updating ticket in Appika...";
            
            // Extract numeric ID from appika_id (e.g., HT076 -> 76)
            $numericId = (int)filter_var($before_data['appika_id'], FILTER_SANITIZE_NUMBER_INT);
            
            if ($numericId > 0) {
                // Prepare update data for Appika API
                $updateData = [
                    'contact_id' => null,
                    'agent_id' => null,
                    'subject' => 'Test Update from Simulation',
                    'type' => 1, // Default type
                    'type_name' => 'starter',
                    'priority' => strtoupper($new_priority),
                    'status' => strtoupper($new_status),
                    'req_email' => '<EMAIL>',
                    'time_track' => '00:00:00',
                    'reply_msg' => "Simulated update: Status to $new_status, Priority to $new_priority",
                    'tags' => 'simulation'
                ];
                
                // Call Appika API to update the ticket
                $apiResult = updateAppikaTicket($numericId, $updateData);
                
                if ($apiResult['success']) {
                    $simulation_result['steps'][] = "✅ Step 1 Complete: Ticket updated in Appika successfully";
                    
                    // Step 2: Wait a moment (simulate real-world delay)
                    $simulation_result['steps'][] = "Step 2: Waiting for changes to propagate...";
                    sleep(2); // Wait 2 seconds
                    
                    // Step 3: Test reverse sync
                    $simulation_result['steps'][] = "Step 3: Testing reverse sync from Appika to local database...";
                    
                    $sync_result = syncFromAppika($ticket_id);
                    $simulation_result['sync_result'] = $sync_result;
                    
                    if ($sync_result['success']) {
                        if ($sync_result['updated']) {
                            $simulation_result['steps'][] = "✅ Step 3 Complete: Reverse sync successful - ticket updated in local database";
                            $simulation_result['success'] = true;
                        } else {
                            $simulation_result['steps'][] = "⚠️ Step 3 Result: Reverse sync ran but no changes detected";
                        }
                    } else {
                        $simulation_result['steps'][] = "❌ Step 3 Failed: " . $sync_result['message'];
                    }
                    
                    // Get ticket data AFTER simulation
                    $stmt = mysqli_prepare($conn, $before_query);
                    mysqli_stmt_bind_param($stmt, 'i', $ticket_id);
                    mysqli_stmt_execute($stmt);
                    $result = mysqli_stmt_get_result($stmt);
                    $after_data = mysqli_fetch_assoc($result);
                    
                } else {
                    $simulation_result['steps'][] = "❌ Step 1 Failed: Could not update ticket in Appika - " . $apiResult['message'];
                }
            } else {
                $simulation_result['steps'][] = "❌ Step 1 Failed: Invalid Appika ID format";
            }
            
            $message = 'Simulation completed!';
            $message_type = 'info';
        } else {
            $message = 'Ticket not found or has no Appika ID!';
            $message_type = 'error';
        }
    } else {
        $message = 'Please enter a valid ticket ID.';
        $message_type = 'warning';
    }
}

// Get tickets with Appika IDs for testing
$query = "SELECT id, subject, appika_id, status, priority, updated_at 
          FROM support_tickets 
          WHERE appika_id IS NOT NULL AND appika_id != '' 
          ORDER BY updated_at DESC 
          LIMIT 10";
$tickets_result = mysqli_query($conn, $query);
$test_tickets = [];
while ($row = mysqli_fetch_assoc($tickets_result)) {
    $test_tickets[] = $row;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulate Appika Updates</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <link rel="stylesheet" href="../css/main.css">
    
    <style>
    .test-container {
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .test-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        padding: 20px;
    }
    
    .step-log {
        background: #f8f9fa;
        border-radius: 5px;
        padding: 15px;
        margin: 10px 0;
        font-family: monospace;
        white-space: pre-line;
    }
    
    .comparison-table {
        width: 100%;
        border-collapse: collapse;
        margin: 15px 0;
    }
    
    .comparison-table th,
    .comparison-table td {
        border: 1px solid #ddd;
        padding: 12px;
        text-align: left;
    }
    
    .comparison-table th {
        background-color: #f8f9fa;
        font-weight: bold;
    }
    
    .changed {
        background-color: #fff3cd !important;
        font-weight: bold;
    }
    
    .ticket-item {
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        margin: 5px 0;
        cursor: pointer;
        transition: background-color 0.3s;
    }
    
    .ticket-item:hover {
        background-color: #f8f9fa;
    }
    
    .warning-box {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 5px;
        padding: 15px;
        margin: 15px 0;
    }
    </style>
</head>

<body>
    <div class="test-container">
        <div class="test-card">
            <h2><i class="fas fa-flask text-primary"></i> Simulate Appika Updates</h2>
            <p class="text-muted">This tool simulates updating a ticket in Appika and tests if it syncs to your database</p>
            
            <div class="warning-box">
                <i class="fas fa-exclamation-triangle text-warning"></i>
                <strong>Note:</strong> This tool actually updates tickets in your Appika system. Use with caution and only on test tickets.
            </div>
            
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type === 'error' ? 'danger' : ($message_type === 'warning' ? 'warning' : 'info'); ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Simulation Form -->
        <div class="test-card">
            <h4>Simulation Settings</h4>
            
            <form method="POST">
                <div class="row">
                    <div class="col-md-3">
                        <label for="ticket_id">Ticket ID:</label>
                        <input type="number" class="form-control" name="ticket_id" id="ticket_id" required min="1" 
                               value="<?php echo isset($_POST['ticket_id']) ? $_POST['ticket_id'] : ''; ?>">
                    </div>
                    <div class="col-md-3">
                        <label for="new_status">New Status:</label>
                        <select class="form-control" name="new_status" id="new_status" required>
                            <option value="OPEN" <?php echo (isset($_POST['new_status']) && $_POST['new_status'] === 'OPEN') ? 'selected' : ''; ?>>Open</option>
                            <option value="WIP" <?php echo (isset($_POST['new_status']) && $_POST['new_status'] === 'WIP') ? 'selected' : ''; ?>>In Progress</option>
                            <option value="CLOSED" <?php echo (isset($_POST['new_status']) && $_POST['new_status'] === 'CLOSED') ? 'selected' : ''; ?>>Closed</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="new_priority">New Priority:</label>
                        <select class="form-control" name="new_priority" id="new_priority" required>
                            <option value="MEDIUM" <?php echo (isset($_POST['new_priority']) && $_POST['new_priority'] === 'MEDIUM') ? 'selected' : ''; ?>>Normal</option>
                            <option value="HIGH" <?php echo (isset($_POST['new_priority']) && $_POST['new_priority'] === 'HIGH') ? 'selected' : ''; ?>>Important</option>
                            <option value="URGENT" <?php echo (isset($_POST['new_priority']) && $_POST['new_priority'] === 'URGENT') ? 'selected' : ''; ?>>Critical</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" name="simulate" class="btn btn-warning">
                            <i class="fas fa-flask"></i> Run Simulation
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Available Test Tickets -->
        <?php if (!empty($test_tickets)): ?>
        <div class="test-card">
            <h4>Available Test Tickets</h4>
            <p class="text-muted">Click on a ticket to select it for simulation:</p>
            
            <?php foreach ($test_tickets as $ticket): ?>
                <div class="ticket-item" onclick="document.getElementById('ticket_id').value = <?php echo $ticket['id']; ?>">
                    <strong>Ticket #<?php echo $ticket['id']; ?></strong> 
                    <span class="badge badge-info"><?php echo htmlspecialchars($ticket['appika_id']); ?></span>
                    <br>
                    <small><?php echo htmlspecialchars($ticket['subject']); ?></small>
                    <br>
                    <small class="text-muted">
                        Current Status: <strong><?php echo ucfirst($ticket['status']); ?></strong> | 
                        Priority: <strong><?php echo ucfirst($ticket['priority']); ?></strong>
                    </small>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <!-- Simulation Results -->
        <?php if ($simulation_result): ?>
        <div class="test-card">
            <h4>Simulation Results</h4>
            
            <!-- Step Log -->
            <h5>Execution Log</h5>
            <div class="step-log">
<?php foreach ($simulation_result['steps'] as $step): ?>
<?php echo htmlspecialchars($step) . "\n"; ?>
<?php endforeach; ?>
            </div>

            <!-- Overall Result -->
            <div class="alert alert-<?php echo $simulation_result['success'] ? 'success' : 'warning'; ?>">
                <strong>
                    <?php if ($simulation_result['success']): ?>
                        🎉 Simulation Successful!
                    <?php else: ?>
                        ⚠️ Simulation Completed with Issues
                    <?php endif; ?>
                </strong>
                <br>
                <?php if ($simulation_result['success']): ?>
                    The ticket was successfully updated in Appika and the changes were synced to your local database.
                <?php else: ?>
                    The simulation ran but there may have been issues. Check the execution log above for details.
                <?php endif; ?>
            </div>

            <!-- Before vs After Comparison -->
            <?php if ($before_data && $after_data): ?>
            <h5>Before vs After Comparison</h5>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Field</th>
                        <th>Before Simulation</th>
                        <th>After Simulation</th>
                        <th>Changed?</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="<?php echo $before_data['status'] !== $after_data['status'] ? 'changed' : ''; ?>">
                        <td><strong>Status</strong></td>
                        <td><?php echo ucfirst($before_data['status']); ?></td>
                        <td><?php echo ucfirst($after_data['status']); ?></td>
                        <td><?php echo $before_data['status'] !== $after_data['status'] ? '✅ Yes' : '❌ No'; ?></td>
                    </tr>
                    <tr class="<?php echo $before_data['priority'] !== $after_data['priority'] ? 'changed' : ''; ?>">
                        <td><strong>Priority</strong></td>
                        <td><?php echo ucfirst($before_data['priority']); ?></td>
                        <td><?php echo ucfirst($after_data['priority']); ?></td>
                        <td><?php echo $before_data['priority'] !== $after_data['priority'] ? '✅ Yes' : '❌ No'; ?></td>
                    </tr>
                    <tr class="<?php echo $before_data['appika_updated_at'] !== $after_data['appika_updated_at'] ? 'changed' : ''; ?>">
                        <td><strong>Appika Updated At</strong></td>
                        <td><?php echo $before_data['appika_updated_at'] ?: 'Never'; ?></td>
                        <td><?php echo $after_data['appika_updated_at'] ?: 'Never'; ?></td>
                        <td><?php echo $before_data['appika_updated_at'] !== $after_data['appika_updated_at'] ? '✅ Yes' : '❌ No'; ?></td>
                    </tr>
                </tbody>
            </table>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- Quick Links -->
        <div class="test-card">
            <h4>Other Testing Tools</h4>
            <a href="test-live-sync.php" class="btn btn-outline-primary mr-2">
                <i class="fas fa-sync"></i> Manual Sync Test
            </a>
            <a href="appika-updates-log.php" class="btn btn-outline-info mr-2">
                <i class="fas fa-list"></i> View Updates Log
            </a>
            <a href="admin-tickets.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Tickets
            </a>
        </div>
    </div>

    <script src="../js/vendor.min.js"></script>
</body>
</html>
