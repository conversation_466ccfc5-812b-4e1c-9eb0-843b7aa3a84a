<?php
/**
 * Diagnose Reverse Sync Setup
 * This script checks if your reverse sync is properly configured
 */

session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

echo "<h2>🔍 Appika Reverse Sync Diagnostic</h2>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.check { color: green; font-weight: bold; }
.error { color: red; font-weight: bold; }
.warning { color: orange; font-weight: bold; }
.info { background: #e8f4fd; padding: 10px; border-radius: 5px; margin: 10px 0; }
.success { background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
.danger { background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
</style>";

// Step 1: Check database columns
echo "<h3>1. Database Columns Check</h3>";
$columns_to_check = ['last_appika_sync', 'appika_updated_at', 'appika_update_source'];
$missing_columns = [];

foreach ($columns_to_check as $column) {
    $result = mysqli_query($conn, "SHOW COLUMNS FROM support_tickets LIKE '$column'");
    if (mysqli_num_rows($result) > 0) {
        echo "<span class='check'>✅</span> Column '$column' exists<br>";
    } else {
        echo "<span class='error'>❌</span> Column '$column' is missing<br>";
        $missing_columns[] = $column;
    }
}

if (!empty($missing_columns)) {
    echo "<div class='danger'>";
    echo "<strong>❌ Missing Database Columns!</strong><br>";
    echo "The reverse sync requires these columns to be added to your support_tickets table.<br>";
    echo "Missing columns: " . implode(', ', $missing_columns) . "<br><br>";
    echo "<strong>Solution:</strong> Run the setup script or add these columns manually:<br>";
    echo "<code>ALTER TABLE support_tickets ADD COLUMN last_appika_sync TIMESTAMP NULL DEFAULT NULL;</code><br>";
    echo "<code>ALTER TABLE support_tickets ADD COLUMN appika_updated_at TIMESTAMP NULL DEFAULT NULL;</code><br>";
    echo "<code>ALTER TABLE support_tickets ADD COLUMN appika_update_source VARCHAR(50) NULL DEFAULT NULL;</code><br>";
    echo "</div>";
} else {
    echo "<div class='success'>✅ All required database columns exist!</div>";
}

// Step 2: Check if reverse sync functions exist
echo "<h3>2. Reverse Sync Functions Check</h3>";
if (file_exists('../functions/appika_reverse_sync.php')) {
    echo "<span class='check'>✅</span> appika_reverse_sync.php file exists<br>";
    
    require_once '../functions/appika_reverse_sync.php';
    
    if (function_exists('syncFromAppika')) {
        echo "<span class='check'>✅</span> syncFromAppika() function exists<br>";
    } else {
        echo "<span class='error'>❌</span> syncFromAppika() function not found<br>";
    }
    
    if (function_exists('bulkSyncFromAppika')) {
        echo "<span class='check'>✅</span> bulkSyncFromAppika() function exists<br>";
    } else {
        echo "<span class='error'>❌</span> bulkSyncFromAppika() function not found<br>";
    }
} else {
    echo "<span class='error'>❌</span> appika_reverse_sync.php file not found<br>";
}

// Step 3: Check API configuration
echo "<h3>3. API Configuration Check</h3>";
if (file_exists('../config/api-config.php')) {
    echo "<span class='check'>✅</span> API configuration file exists<br>";
    
    require_once '../config/api-config.php';
    $config = getGraphqlApiConfig();
    
    if (!empty($config['endpoint'])) {
        echo "<span class='check'>✅</span> API endpoint configured<br>";
    } else {
        echo "<span class='error'>❌</span> API endpoint not configured<br>";
    }
    
    if (!empty($config['key'])) {
        echo "<span class='check'>✅</span> API key configured<br>";
    } else {
        echo "<span class='error'>❌</span> API key not configured<br>";
    }
} else {
    echo "<span class='error'>❌</span> API configuration file not found<br>";
}

// Step 4: Check for tickets with Appika IDs
echo "<h3>4. Test Data Check</h3>";
$query = "SELECT COUNT(*) as count FROM support_tickets WHERE appika_id IS NOT NULL AND appika_id != ''";
$result = mysqli_query($conn, $query);
$row = mysqli_fetch_assoc($result);
$ticket_count = $row['count'];

if ($ticket_count > 0) {
    echo "<span class='check'>✅</span> Found $ticket_count tickets with Appika IDs<br>";
    
    // Show sample tickets
    $sample_query = "SELECT id, appika_id, status, priority, updated_at FROM support_tickets WHERE appika_id IS NOT NULL AND appika_id != '' LIMIT 5";
    $sample_result = mysqli_query($conn, $sample_query);
    
    echo "<strong>Sample tickets for testing:</strong><br>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Appika ID</th><th>Status</th><th>Priority</th><th>Updated</th></tr>";
    while ($ticket = mysqli_fetch_assoc($sample_result)) {
        echo "<tr>";
        echo "<td>{$ticket['id']}</td>";
        echo "<td>{$ticket['appika_id']}</td>";
        echo "<td>{$ticket['status']}</td>";
        echo "<td>{$ticket['priority']}</td>";
        echo "<td>{$ticket['updated_at']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<span class='warning'>⚠️</span> No tickets with Appika IDs found<br>";
    echo "<div class='info'>You need tickets that have been synced to Appika (have appika_id values) to test reverse sync.</div>";
}

// Step 5: Test reverse sync function
echo "<h3>5. Function Test</h3>";
if (empty($missing_columns) && $ticket_count > 0 && function_exists('syncFromAppika')) {
    // Get a test ticket
    $test_query = "SELECT id, appika_id FROM support_tickets WHERE appika_id IS NOT NULL AND appika_id != '' LIMIT 1";
    $test_result = mysqli_query($conn, $test_query);
    $test_ticket = mysqli_fetch_assoc($test_result);
    
    if ($test_ticket) {
        echo "Testing with Ticket ID: {$test_ticket['id']} (Appika ID: {$test_ticket['appika_id']})<br>";
        
        try {
            $sync_result = syncFromAppika($test_ticket['id']);
            
            if ($sync_result['success']) {
                echo "<span class='check'>✅</span> Sync function works! Message: " . $sync_result['message'] . "<br>";
                if ($sync_result['updated']) {
                    echo "<span class='check'>🔄</span> Ticket was updated during test<br>";
                }
            } else {
                echo "<span class='error'>❌</span> Sync function failed: " . $sync_result['message'] . "<br>";
            }
        } catch (Exception $e) {
            echo "<span class='error'>❌</span> Error testing sync function: " . $e->getMessage() . "<br>";
        }
    }
} else {
    echo "<span class='warning'>⚠️</span> Cannot test - missing requirements<br>";
}

// Step 6: Summary and recommendations
echo "<h3>6. Summary & Next Steps</h3>";

if (empty($missing_columns) && $ticket_count > 0 && function_exists('syncFromAppika')) {
    echo "<div class='success'>";
    echo "<strong>✅ Your reverse sync setup looks good!</strong><br><br>";
    echo "<strong>To test reverse sync:</strong><br>";
    echo "1. Go to your Appika system and update a ticket's status or priority<br>";
    echo "2. Use the test tool: <a href='test-reverse-sync.php'>test-reverse-sync.php</a><br>";
    echo "3. Or view a ticket: <a href='admin-ticket-detail.php?id={$test_ticket['id']}'>admin-ticket-detail.php?id={$test_ticket['id']}</a><br>";
    echo "</div>";
} else {
    echo "<div class='danger'>";
    echo "<strong>❌ Setup Issues Found</strong><br><br>";
    echo "<strong>Required actions:</strong><br>";
    
    if (!empty($missing_columns)) {
        echo "• Add missing database columns (see above)<br>";
    }
    
    if ($ticket_count == 0) {
        echo "• Create tickets with Appika IDs (sync some tickets to Appika first)<br>";
    }
    
    if (!function_exists('syncFromAppika')) {
        echo "• Fix reverse sync function files<br>";
    }
    
    echo "<br><strong>Quick fix:</strong> Run the setup script to add missing columns<br>";
    echo "</div>";
}

echo "<h3>7. Manual Setup (if needed)</h3>";
echo "<div class='info'>";
echo "If you need to add the missing columns manually, run these SQL commands:<br><br>";
echo "<code>ALTER TABLE support_tickets ADD COLUMN last_appika_sync TIMESTAMP NULL DEFAULT NULL;</code><br>";
echo "<code>ALTER TABLE support_tickets ADD COLUMN appika_updated_at TIMESTAMP NULL DEFAULT NULL;</code><br>";
echo "<code>ALTER TABLE support_tickets ADD COLUMN appika_update_source VARCHAR(50) NULL DEFAULT NULL;</code><br>";
echo "</div>";
?>
