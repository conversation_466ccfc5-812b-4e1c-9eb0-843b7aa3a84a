<?php
/**
 * Diagnose Chat Sync Issues
 * This tool helps diagnose why Appika sync might not be working when admin sends chat messages
 */

session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

require_once '../functions/appika_sync.php';

echo "<h2>🔍 Chat Sync Diagnostic Tool</h2>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.check { color: green; font-weight: bold; }
.error { color: red; font-weight: bold; }
.warning { color: orange; font-weight: bold; }
.info { background: #e8f4fd; padding: 10px; border-radius: 5px; margin: 10px 0; }
.success { background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
.danger { background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
.log-entry { background: #f8f9fa; padding: 8px; margin: 5px 0; border-left: 3px solid #007bff; font-family: monospace; }
</style>";

// Step 1: Check recent chat sync logs
echo "<h3>1. Recent Chat Sync Logs</h3>";
$logFile = '../logs/appika_sync.log';

if (file_exists($logFile)) {
    $logs = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $chatLogs = array_filter($logs, function($line) {
        return strpos($line, 'admin_chat_status_update') !== false;
    });
    
    if (!empty($chatLogs)) {
        echo "<p>Found " . count($chatLogs) . " chat sync log entries:</p>";
        $recentLogs = array_slice(array_reverse($chatLogs), 0, 5); // Last 5 entries
        
        foreach ($recentLogs as $log) {
            $logData = json_decode($log, true);
            if ($logData) {
                $status = $logData['appika_updated'] ? '✅ Success' : '❌ Failed';
                echo "<div class='log-entry'>";
                echo "<strong>$status</strong> - Ticket #{$logData['ticket_id']} - {$logData['timestamp']}<br>";
                echo "Message: {$logData['message']}";
                echo "</div>";
            }
        }
    } else {
        echo "<span class='warning'>⚠️</span> No chat sync logs found. This might indicate the sync isn't being triggered.";
    }
} else {
    echo "<span class='warning'>⚠️</span> No sync log file found at: $logFile";
}

// Step 2: Check tickets with recent status changes to in_progress
echo "<h3>2. Recent Status Changes to In Progress</h3>";
$query = "SELECT id, subject, status, updated_at, appika_id 
          FROM support_tickets 
          WHERE status = 'in_progress' 
          AND updated_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
          ORDER BY updated_at DESC 
          LIMIT 10";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Subject</th><th>Appika ID</th><th>Updated At</th><th>Test Sync</th></tr>";
    
    while ($ticket = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>{$ticket['id']}</td>";
        echo "<td>" . htmlspecialchars($ticket['subject']) . "</td>";
        echo "<td>" . ($ticket['appika_id'] ?: 'None') . "</td>";
        echo "<td>{$ticket['updated_at']}</td>";
        echo "<td><a href='?test_ticket={$ticket['id']}' class='btn btn-sm btn-primary'>Test Sync</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<span class='warning'>⚠️</span> No tickets with 'in_progress' status updated in the last 24 hours.";
}

// Step 3: Test sync for a specific ticket
if (isset($_GET['test_ticket'])) {
    $test_ticket_id = intval($_GET['test_ticket']);
    echo "<h3>3. Testing Sync for Ticket #$test_ticket_id</h3>";
    
    // Get ticket details
    $ticket_query = "SELECT * FROM support_tickets WHERE id = ?";
    $stmt = mysqli_prepare($conn, $ticket_query);
    mysqli_stmt_bind_param($stmt, 'i', $test_ticket_id);
    mysqli_stmt_execute($stmt);
    $ticket_result = mysqli_stmt_get_result($stmt);
    $ticket = mysqli_fetch_assoc($ticket_result);
    
    if ($ticket) {
        echo "<div class='info'>";
        echo "<strong>Ticket Details:</strong><br>";
        echo "ID: {$ticket['id']}<br>";
        echo "Subject: " . htmlspecialchars($ticket['subject']) . "<br>";
        echo "Status: {$ticket['status']}<br>";
        echo "Priority: {$ticket['priority']}<br>";
        echo "Appika ID: " . ($ticket['appika_id'] ?: 'None') . "<br>";
        echo "Updated: {$ticket['updated_at']}<br>";
        echo "</div>";
        
        if (empty($ticket['appika_id'])) {
            echo "<div class='danger'>";
            echo "<strong>❌ Cannot Test Sync</strong><br>";
            echo "This ticket has no Appika ID. The ticket was never synced to Appika, so status updates won't sync either.";
            echo "</div>";
        } else {
            echo "<h4>Testing Sync to Appika...</h4>";
            
            try {
                $syncResult = syncTicketToAppika($test_ticket_id, 'in_progress', null, $_SESSION['admin_username']);
                
                if ($syncResult['appika_updated']) {
                    echo "<div class='success'>";
                    echo "<strong>✅ Sync Successful!</strong><br>";
                    echo "Message: " . htmlspecialchars($syncResult['message']);
                    echo "</div>";
                } else {
                    echo "<div class='danger'>";
                    echo "<strong>❌ Sync Failed</strong><br>";
                    echo "Message: " . htmlspecialchars($syncResult['message']);
                    echo "</div>";
                }
            } catch (Exception $e) {
                echo "<div class='danger'>";
                echo "<strong>❌ Sync Error</strong><br>";
                echo "Exception: " . htmlspecialchars($e->getMessage());
                echo "</div>";
            }
        }
    } else {
        echo "<span class='error'>❌</span> Ticket not found.";
    }
}

// Step 4: Check API configuration
echo "<h3>4. API Configuration Check</h3>";
if (file_exists('../config/api-config.php')) {
    require_once '../config/api-config.php';
    $config = getGraphqlApiConfig();
    
    if (!empty($config['endpoint'])) {
        echo "<span class='check'>✅</span> API endpoint configured<br>";
    } else {
        echo "<span class='error'>❌</span> API endpoint not configured<br>";
    }
    
    if (!empty($config['key'])) {
        echo "<span class='check'>✅</span> API key configured<br>";
    } else {
        echo "<span class='error'>❌</span> API key not configured<br>";
    }
} else {
    echo "<span class='error'>❌</span> API configuration file not found<br>";
}

// Step 5: Check function availability
echo "<h3>5. Function Availability Check</h3>";
if (function_exists('syncTicketToAppika')) {
    echo "<span class='check'>✅</span> syncTicketToAppika function available<br>";
} else {
    echo "<span class='error'>❌</span> syncTicketToAppika function not found<br>";
}

if (function_exists('mapStatusToAppika')) {
    echo "<span class='check'>✅</span> mapStatusToAppika function available<br>";
    
    // Test the mapping
    $mapped = mapStatusToAppika('in_progress');
    if ($mapped === 'WIP') {
        echo "<span class='check'>✅</span> Status mapping correct: 'in_progress' → 'WIP'<br>";
    } else {
        echo "<span class='error'>❌</span> Status mapping incorrect: 'in_progress' → '$mapped' (should be 'WIP')<br>";
    }
} else {
    echo "<span class='error'>❌</span> mapStatusToAppika function not found<br>";
}

// Step 6: Recommendations
echo "<h3>6. Troubleshooting Steps</h3>";
echo "<div class='info'>";
echo "<strong>To test the chat sync issue:</strong><br>";
echo "1. Create a test ticket (or use an existing one with Appika ID)<br>";
echo "2. Make sure the ticket status is 'Open'<br>";
echo "3. Go to admin-chat.php and send a message to that ticket<br>";
echo "4. Check if the status changes to 'In Progress' locally<br>";
echo "5. Check the sync logs above for any error messages<br>";
echo "6. Use the 'Test Sync' button above to manually test the sync<br><br>";

echo "<strong>Common issues:</strong><br>";
echo "• Ticket has no Appika ID (never synced to Appika initially)<br>";
echo "• API configuration problems (endpoint/key)<br>";
echo "• Network connectivity issues<br>";
echo "• Appika API errors or rate limiting<br>";
echo "</div>";

echo "<h3>7. Quick Links</h3>";
echo "<a href='admin-chat.php' class='btn btn-primary'>Go to Chat</a> ";
echo "<a href='appika-updates-log.php' class='btn btn-info'>View Sync Log</a> ";
echo "<a href='simulate-appika-update.php' class='btn btn-warning'>Test Simulation</a>";
?>

<style>
.btn {
    display: inline-block;
    padding: 6px 12px;
    margin: 2px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    text-decoration: none;
}

.btn-primary { background-color: #473BF0; color: white; border-color: #473BF0; }
.btn-info { background-color: #17a2b8; color: white; border-color: #17a2b8; }
.btn-warning { background-color: #ffc107; color: #212529; border-color: #ffc107; }
.btn-sm { padding: 4px 8px; font-size: 12px; }

table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
</style>
