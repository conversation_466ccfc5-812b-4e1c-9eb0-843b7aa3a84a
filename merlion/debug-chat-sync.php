<?php
/**
 * Debug Chat Sync - Add temporary logging to see what's happening
 * This will help us understand why the sync isn't being called
 */

session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

require_once '../functions/appika_sync.php';

echo "<h2>🐛 Debug Chat Sync Issue</h2>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.debug { background: #f8f9fa; padding: 10px; margin: 10px 0; border-left: 3px solid #007bff; }
.error { background: #f8d7da; padding: 10px; margin: 10px 0; border-left: 3px solid #dc3545; }
.success { background: #d4edda; padding: 10px; margin: 10px 0; border-left: 3px solid #28a745; }
</style>";

// Step 1: Check if we can reproduce the issue
if (isset($_GET['test_ticket'])) {
    $ticket_id = intval($_GET['test_ticket']);
    
    echo "<h3>Testing Ticket #$ticket_id</h3>";
    
    // Get ticket current status
    $query = "SELECT id, status, assigned_admin_id, appika_id FROM support_tickets WHERE id = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $ticket_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $ticket = mysqli_fetch_assoc($result);
    
    if (!$ticket) {
        echo "<div class='error'>❌ Ticket not found</div>";
        exit;
    }
    
    echo "<div class='debug'>";
    echo "<strong>Current Ticket Status:</strong><br>";
    echo "ID: {$ticket['id']}<br>";
    echo "Status: {$ticket['status']}<br>";
    echo "Assigned Admin: " . ($ticket['assigned_admin_id'] ?: 'None') . "<br>";
    echo "Appika ID: " . ($ticket['appika_id'] ?: 'None') . "<br>";
    echo "</div>";
    
    // Simulate the chat logic
    $current_status = $ticket['status'];
    $current_assigned_admin = $ticket['assigned_admin_id'];
    $admin_id = $_SESSION['admin_id'];
    $admin_username = $_SESSION['admin_username'];
    
    echo "<h4>Simulating Chat Logic:</h4>";
    
    // Check the conditions
    $condition1 = ($current_status === 'open');
    $condition2 = ($current_assigned_admin === null || $current_assigned_admin == 0);
    
    echo "<div class='debug'>";
    echo "<strong>Condition Checks:</strong><br>";
    echo "current_status === 'open': " . ($condition1 ? '✅ TRUE' : '❌ FALSE') . " (current: '$current_status')<br>";
    echo "assigned_admin is null/0: " . ($condition2 ? '✅ TRUE' : '❌ FALSE') . " (current: '$current_assigned_admin')<br>";
    echo "Overall condition: " . (($condition1 || $condition2) ? '✅ TRUE' : '❌ FALSE') . "<br>";
    echo "</div>";
    
    if ($current_status === 'open' || $condition2) {
        echo "<div class='success'>✅ Conditions met - sync should be triggered</div>";
        
        // Determine what would happen
        if ($current_status === 'open' && $condition2) {
            echo "<div class='debug'>Would update: Both status and assignment</div>";
            $status_updated = true;
        } elseif ($current_status === 'open') {
            echo "<div class='debug'>Would update: Status only</div>";
            $status_updated = true;
        } elseif ($condition2) {
            echo "<div class='debug'>Would update: Assignment only</div>";
            $status_updated = false;
        }
        
        if (isset($status_updated) && $status_updated) {
            echo "<div class='success'>✅ status_updated = TRUE - Appika sync should be called</div>";
            
            // Test the actual sync
            if (empty($ticket['appika_id'])) {
                echo "<div class='error'>❌ Cannot sync: No Appika ID</div>";
            } else {
                echo "<div class='debug'>Testing actual sync call...</div>";
                try {
                    $syncResult = syncTicketToAppika($ticket_id, 'in_progress', null, $admin_username);
                    
                    if ($syncResult['appika_updated']) {
                        echo "<div class='success'>✅ Sync successful: " . htmlspecialchars($syncResult['message']) . "</div>";
                    } else {
                        echo "<div class='error'>❌ Sync failed: " . htmlspecialchars($syncResult['message']) . "</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='error'>❌ Sync exception: " . htmlspecialchars($e->getMessage()) . "</div>";
                }
            }
        } else {
            echo "<div class='error'>❌ status_updated = FALSE - Appika sync would NOT be called</div>";
        }
    } else {
        echo "<div class='error'>❌ Conditions not met - sync would not be triggered</div>";
        echo "<div class='debug'>";
        echo "<strong>To trigger sync, ticket needs:</strong><br>";
        echo "• Status = 'open' (currently: '$current_status'), OR<br>";
        echo "• No assigned admin (currently: " . ($current_assigned_admin ?: 'None') . ")<br>";
        echo "</div>";
    }
}

// Step 2: Show tickets that would trigger sync
echo "<h3>Tickets That Would Trigger Sync</h3>";

$query = "SELECT id, subject, status, assigned_admin_id, appika_id, updated_at 
          FROM support_tickets 
          WHERE (status = 'open' OR assigned_admin_id IS NULL OR assigned_admin_id = 0)
          AND appika_id IS NOT NULL AND appika_id != ''
          ORDER BY updated_at DESC 
          LIMIT 10";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Subject</th><th>Status</th><th>Assigned</th><th>Appika ID</th><th>Test</th></tr>";
    
    while ($ticket = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>{$ticket['id']}</td>";
        echo "<td>" . htmlspecialchars(substr($ticket['subject'], 0, 30)) . "...</td>";
        echo "<td>{$ticket['status']}</td>";
        echo "<td>" . ($ticket['assigned_admin_id'] ?: 'None') . "</td>";
        echo "<td>{$ticket['appika_id']}</td>";
        echo "<td><a href='?test_ticket={$ticket['id']}'>Test</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<div class='debug'>No tickets found that would trigger sync (need status='open' OR no assigned admin, AND have Appika ID)</div>";
}

// Step 3: Show recent tickets that changed to in_progress
echo "<h3>Recent Status Changes to In Progress</h3>";

$query = "SELECT id, subject, status, assigned_admin_id, appika_id, updated_at 
          FROM support_tickets 
          WHERE status = 'in_progress' 
          AND updated_at >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
          ORDER BY updated_at DESC 
          LIMIT 5";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Subject</th><th>Assigned</th><th>Appika ID</th><th>Updated</th><th>Test</th></tr>";
    
    while ($ticket = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>{$ticket['id']}</td>";
        echo "<td>" . htmlspecialchars(substr($ticket['subject'], 0, 30)) . "...</td>";
        echo "<td>" . ($ticket['assigned_admin_id'] ?: 'None') . "</td>";
        echo "<td>" . ($ticket['appika_id'] ?: 'None') . "</td>";
        echo "<td>{$ticket['updated_at']}</td>";
        echo "<td><a href='?test_ticket={$ticket['id']}'>Test</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<div class='debug'>No tickets changed to 'in_progress' in the last 2 hours</div>";
}

// Step 4: Instructions
echo "<h3>How to Test</h3>";
echo "<div class='debug'>";
echo "<strong>To test the chat sync issue:</strong><br>";
echo "1. Find a ticket with status 'open' and Appika ID from the table above<br>";
echo "2. Click 'Test' to see if the sync logic would work<br>";
echo "3. If the test shows it should work, try sending an actual chat message<br>";
echo "4. Check the sync logs after sending the message<br><br>";

echo "<strong>Expected behavior:</strong><br>";
echo "• When admin sends chat to 'open' ticket → status changes to 'in_progress'<br>";
echo "• If ticket has Appika ID → sync call should be made<br>";
echo "• Sync log entry should appear with 'admin_chat_status_update'<br>";
echo "</div>";

echo "<h3>Quick Links</h3>";
echo "<a href='admin-chat.php'>Go to Chat</a> | ";
echo "<a href='diagnose-chat-sync.php'>Full Diagnostic</a> | ";
echo "<a href='../logs/appika_sync.log' target='_blank'>View Sync Log</a>";
?>

<style>
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f8f9fa; }
a { color: #473BF0; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
