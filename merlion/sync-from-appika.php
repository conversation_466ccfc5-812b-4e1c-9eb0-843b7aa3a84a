<?php
/**
 * Scheduled Sync Script - Run via cron job
 * Syncs updates FROM Appika TO local database
 * 
 * Usage: php sync-from-appika.php
 * Cron: Run every 10 minutes with /usr/bin/php /path/to/sync-from-appika.php
 */

// Include required files
include('../functions/server.php');
require_once '../functions/appika_reverse_sync.php';

// Set execution time limit for long-running sync
set_time_limit(300); // 5 minutes max

// Log file for sync operations
$logFile = '../logs/appika_sync_' . date('Y-m-d') . '.log';

// Ensure logs directory exists
if (!file_exists('../logs')) {
    mkdir('../logs', 0755, true);
}

// Function to write to log
function writeLog($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

// Start sync process
writeLog("=== Starting Appika Reverse Sync ===");

try {
    // Perform bulk sync
    $results = bulkSyncFromAppika(100); // Check up to 100 tickets
    
    // Log results
    writeLog("Sync completed:");
    writeLog("- Total tickets checked: " . $results['total_checked']);
    writeLog("- Total tickets updated: " . $results['total_updated']);
    
    if (!empty($results['errors'])) {
        writeLog("- Errors encountered:");
        foreach ($results['errors'] as $error) {
            writeLog("  * $error");
        }
    }
    
    // Output for cron job logs
    echo "Appika Sync: {$results['total_checked']} checked, {$results['total_updated']} updated";
    if (!empty($results['errors'])) {
        echo ", " . count($results['errors']) . " errors";
    }
    echo "\n";
    
} catch (Exception $e) {
    $errorMsg = "Fatal error during sync: " . $e->getMessage();
    writeLog($errorMsg);
    echo $errorMsg . "\n";
    exit(1);
}

writeLog("=== Sync completed successfully ===\n");
?>
