<?php
/**
 * Test Appika Reverse Sync Functionality
 * This script helps you test if updates from Appika are syncing to your local database
 */

session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

require_once '../functions/appika_reverse_sync.php';

$message = '';
$message_type = '';
$test_results = [];

// Handle test form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['test_single'])) {
        $ticket_id = intval($_POST['ticket_id']);
        
        if ($ticket_id > 0) {
            $result = syncFromAppika($ticket_id);
            $test_results[] = [
                'type' => 'Single Ticket Reverse Sync Test',
                'ticket_id' => $ticket_id,
                'result' => $result
            ];
            $message = 'Single ticket sync test completed!';
            $message_type = 'info';
        } else {
            $message = 'Please enter a valid ticket ID.';
            $message_type = 'warning';
        }
    } elseif (isset($_POST['test_bulk'])) {
        $limit = intval($_POST['limit']) ?: 10;
        $result = bulkSyncFromAppika($limit);
        $test_results[] = [
            'type' => 'Bulk Reverse Sync Test',
            'result' => $result
        ];
        $message = 'Bulk sync test completed!';
        $message_type = 'info';
    }
}

// Get recent tickets with Appika IDs for testing
$query = "SELECT id, subject, appika_id, status, priority, updated_at 
          FROM support_tickets 
          WHERE appika_id IS NOT NULL AND appika_id != '' 
          ORDER BY updated_at DESC 
          LIMIT 10";
$tickets_result = mysqli_query($conn, $query);
$test_tickets = [];
while ($row = mysqli_fetch_assoc($tickets_result)) {
    $test_tickets[] = $row;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Appika Reverse Sync</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <link rel="stylesheet" href="../css/main.css">
    
    <style>
    .test-container {
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .test-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        padding: 20px;
    }
    
    .result-box {
        background: #f8f9fa;
        border-radius: 5px;
        padding: 15px;
        margin: 10px 0;
        border-left: 4px solid #007bff;
    }
    
    .success { border-left-color: #28a745; }
    .error { border-left-color: #dc3545; }
    .warning { border-left-color: #ffc107; }
    
    .ticket-list {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
    }
    
    .ticket-item {
        padding: 8px;
        border-bottom: 1px solid #eee;
        cursor: pointer;
    }
    
    .ticket-item:hover {
        background-color: #f8f9fa;
    }
    
    .ticket-item:last-child {
        border-bottom: none;
    }
    </style>
</head>

<body>
    <div class="test-container">
        <div class="test-card">
            <h2><i class="fas fa-sync-alt text-primary"></i> Test Appika Reverse Sync</h2>
            <p class="text-muted">Test if updates from Appika are syncing to your local database</p>
            
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type === 'warning' ? 'warning' : 'info'; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Test Forms -->
        <div class="row">
            <div class="col-md-6">
                <div class="test-card">
                    <h4>Test Single Ticket Sync</h4>
                    <p class="text-muted">Test reverse sync for a specific ticket</p>
                    
                    <form method="POST">
                        <div class="form-group">
                            <label for="ticket_id">Ticket ID:</label>
                            <input type="number" class="form-control" name="ticket_id" id="ticket_id" required min="1">
                        </div>
                        <button type="submit" name="test_single" class="btn btn-primary">
                            <i class="fas fa-sync"></i> Test Single Sync
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="test-card">
                    <h4>Test Bulk Sync</h4>
                    <p class="text-muted">Test reverse sync for multiple tickets</p>
                    
                    <form method="POST">
                        <div class="form-group">
                            <label for="limit">Number of tickets to check:</label>
                            <input type="number" class="form-control" name="limit" id="limit" value="10" min="1" max="100">
                        </div>
                        <button type="submit" name="test_bulk" class="btn btn-success">
                            <i class="fas fa-sync-alt"></i> Test Bulk Sync
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Available Test Tickets -->
        <?php if (!empty($test_tickets)): ?>
        <div class="test-card">
            <h4>Available Test Tickets</h4>
            <p class="text-muted">Tickets with Appika IDs that you can test with:</p>
            
            <div class="ticket-list">
                <?php foreach ($test_tickets as $ticket): ?>
                    <div class="ticket-item" onclick="document.getElementById('ticket_id').value = <?php echo $ticket['id']; ?>">
                        <strong>Ticket #<?php echo $ticket['id']; ?></strong> 
                        <span class="badge badge-info"><?php echo htmlspecialchars($ticket['appika_id']); ?></span>
                        <br>
                        <small><?php echo htmlspecialchars($ticket['subject']); ?></small>
                        <br>
                        <small class="text-muted">
                            Status: <?php echo ucfirst($ticket['status']); ?> | 
                            Priority: <?php echo ucfirst($ticket['priority']); ?> | 
                            Updated: <?php echo $ticket['updated_at']; ?>
                        </small>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Test Results -->
        <?php if (!empty($test_results)): ?>
        <div class="test-card">
            <h4>Test Results</h4>
            
            <?php foreach ($test_results as $test): ?>
                <div class="result-box <?php echo $test['result']['success'] ? 'success' : 'error'; ?>">
                    <h5><?php echo htmlspecialchars($test['type']); ?></h5>
                    
                    <?php if (isset($test['ticket_id'])): ?>
                        <p><strong>Ticket ID:</strong> <?php echo $test['ticket_id']; ?></p>
                    <?php endif; ?>
                    
                    <p><strong>Success:</strong> <?php echo $test['result']['success'] ? 'Yes' : 'No'; ?></p>
                    <p><strong>Message:</strong> <?php echo htmlspecialchars($test['result']['message']); ?></p>
                    
                    <?php if (isset($test['result']['updated']) && $test['result']['updated']): ?>
                        <p><strong>Updated:</strong> Yes</p>
                        <?php if (!empty($test['result']['changes'])): ?>
                            <p><strong>Changes:</strong> <?php echo implode(', ', array_keys($test['result']['changes'])); ?></p>
                        <?php endif; ?>
                    <?php endif; ?>
                    
                    <?php if (isset($test['result']['total_checked'])): ?>
                        <p><strong>Total Checked:</strong> <?php echo $test['result']['total_checked']; ?></p>
                        <p><strong>Total Updated:</strong> <?php echo $test['result']['total_updated']; ?></p>
                        <?php if (!empty($test['result']['errors'])): ?>
                            <p><strong>Errors:</strong></p>
                            <ul>
                                <?php foreach ($test['result']['errors'] as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <!-- Instructions -->
        <div class="test-card">
            <h4>How to Test Reverse Sync</h4>
            <ol>
                <li><strong>Update a ticket in Appika:</strong> Go to your Appika system and change the status or priority of a ticket</li>
                <li><strong>Test the sync:</strong> Use the forms above to test if the changes sync to your local database</li>
                <li><strong>Check the results:</strong> Look for "Updated: Yes" and the specific changes that were synced</li>
                <li><strong>View logs:</strong> Check the logs directory for detailed sync information</li>
            </ol>
            
            <div class="mt-3">
                <a href="appika-updates-log.php" class="btn btn-outline-primary">
                    <i class="fas fa-list"></i> View Updates Log
                </a>
                <a href="admin-tickets.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Tickets
                </a>
            </div>
        </div>
    </div>

    <script src="../js/vendor.min.js"></script>
</body>
</html>
