<?php
session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

$admin_username = $_SESSION['admin_username'];

// Include the widget functions
include('appika-updates-widget.php');

// Pagination settings
$limit = 20;
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$offset = ($page - 1) * $limit;

// Time filter
$timeFilter = isset($_GET['time']) ? $_GET['time'] : '24h';
$whereClause = '';

switch ($timeFilter) {
    case '1h':
        $whereClause = "WHERE st.appika_updated_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)";
        break;
    case '24h':
        $whereClause = "WHERE st.appika_updated_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)";
        break;
    case '7d':
        $whereClause = "WHERE st.appika_updated_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
        break;
    case '30d':
        $whereClause = "WHERE st.appika_updated_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
        break;
    case 'all':
    default:
        $whereClause = "WHERE st.appika_updated_at IS NOT NULL";
        break;
}

// Check if columns exist
$columnsExist = checkAppikaColumnsExist();

$updates = [];
$totalCount = 0;

if ($columnsExist) {
    // Get total count
    $countQuery = "SELECT COUNT(*) as total
                   FROM support_tickets st
                   JOIN user u ON st.user_id = u.id
                   $whereClause";
    $countResult = mysqli_query($conn, $countQuery);
    $totalCount = mysqli_fetch_assoc($countResult)['total'];

    // Get updates with pagination
    $query = "SELECT st.id, st.subject, st.status, st.priority, st.appika_id, 
                     st.appika_updated_at, st.appika_update_source,
                     u.username, u.email
              FROM support_tickets st
              JOIN user u ON st.user_id = u.id
              $whereClause
              ORDER BY st.appika_updated_at DESC
              LIMIT $limit OFFSET $offset";
    
    $result = mysqli_query($conn, $query);
    while ($row = mysqli_fetch_assoc($result)) {
        $updates[] = $row;
    }
}

$totalPages = ceil($totalCount / $limit);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appika Updates Log - Admin</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    
    <style>
    body {
        background-color: #f8f9fa;
    }
    
    .admin-container {
        padding: 20px;
        max-width: 100%;
    }
    
    .admin-header {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .filter-tabs {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        padding: 15px 20px;
    }
    
    .filter-tabs .nav-link {
        color: #666;
        border: none;
        padding: 8px 16px;
        margin-right: 5px;
        border-radius: 20px;
        transition: all 0.3s;
    }
    
    .filter-tabs .nav-link.active {
        background-color: #473BF0;
        color: white;
    }
    
    .filter-tabs .nav-link:hover {
        background-color: #f8f9fa;
        color: #473BF0;
    }
    
    .updates-container {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        padding: 20px;
    }
    
    .update-row {
        border-bottom: 1px solid #eee;
        padding: 15px 0;
    }
    
    .update-row:last-child {
        border-bottom: none;
    }
    
    .update-meta {
        font-size: 0.9em;
        color: #666;
    }
    
    .badge-info {
        background-color: #473BF0;
        font-family: 'Courier New', monospace;
        font-weight: bold;
    }
    
    .pagination {
        justify-content: center;
        margin-top: 20px;
    }
    
    .page-link {
        color: #473BF0;
    }
    
    .page-item.active .page-link {
        background-color: #473BF0;
        border-color: #473BF0;
    }
    </style>
</head>

<body>
    <div class="admin-container">
        <!-- Header -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-sync-alt text-primary"></i> Appika Updates Log</h2>
                    <p class="mb-0 text-muted">Track all tickets updated from Appika system</p>
                </div>
                <div>
                    <a href="admin-tickets.php" class="btn btn-outline-secondary" style="background-color: #473BF0 !important; color: white;">
                        <i class="fas fa-arrow-left"></i>&nbsp;Back to Tickets
                    </a>
                </div>
            </div>
        </div>

        <?php if (!$columnsExist): ?>
            <!-- Setup Notice -->
            <div class="updates-container">
                <div class="text-center py-5">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h4>Setup Required</h4>
                    <p class="text-muted mb-4">Appika reverse sync is not set up yet. Run the setup script to enable automatic updates from Appika.</p>
                    <a href="../setup/setup-reverse-sync.php" class="btn btn-warning">
                        <i class="fas fa-cog"></i> Run Setup
                    </a>
                </div>
            </div>
        <?php else: ?>
            <!-- Time Filter Tabs -->
            <div class="filter-tabs">
                <ul class="nav nav-pills">
                    <li class="nav-item">
                        <a class="nav-link <?php echo $timeFilter === '1h' ? 'active' : ''; ?>" href="?time=1h">Last Hour</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $timeFilter === '24h' ? 'active' : ''; ?>" href="?time=24h">Last 24 Hours</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $timeFilter === '7d' ? 'active' : ''; ?>" href="?time=7d">Last 7 Days</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $timeFilter === '30d' ? 'active' : ''; ?>" href="?time=30d">Last 30 Days</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $timeFilter === 'all' ? 'active' : ''; ?>" href="?time=all">All Time</a>
                    </li>
                </ul>
            </div>

            <!-- Updates Container -->
            <div class="updates-container">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">
                        <i class="fas fa-list text-primary"></i> 
                        Updates Log 
                        <span class="badge badge-secondary"><?php echo $totalCount; ?> total</span>
                    </h5>
                    <?php if ($totalCount > 0): ?>
                        <small class="text-muted">
                            Showing <?php echo $offset + 1; ?>-<?php echo min($offset + $limit, $totalCount); ?> of <?php echo $totalCount; ?>
                        </small>
                    <?php endif; ?>
                </div>

                <!-- Updates List -->
                <?php if (empty($updates)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5>No Appika updates found</h5>
                        <p class="text-muted">No tickets have been updated from Appika in the selected time period.</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($updates as $update): ?>
                        <div class="update-row">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="d-flex align-items-center mb-2">
                                        <a href="admin-ticket-detail.php?id=<?php echo $update['id']; ?>" class="h6 mb-0 mr-3">
                                            Ticket #<?php echo $update['id']; ?>
                                        </a>
                                        <?php if (!empty($update['appika_id'])): ?>
                                            <span class="badge badge-info mr-2"><?php echo htmlspecialchars($update['appika_id']); ?></span>
                                        <?php endif; ?>
                                        <span class="badge badge-<?php echo $update['status']; ?> mr-2"><?php echo ucfirst($update['status']); ?></span>
                                        <span class="badge badge-<?php echo strtolower($update['priority']); ?>"><?php echo ucfirst($update['priority']); ?></span>
                                    </div>
                                    <p class="mb-1"><?php echo htmlspecialchars($update['subject']); ?></p>
                                    <div class="update-meta">
                                        <i class="fas fa-user"></i> <?php echo htmlspecialchars($update['username']); ?>
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-envelope"></i> <?php echo htmlspecialchars($update['email']); ?>
                                    </div>
                                </div>
                                <div class="col-md-4 text-md-right">
                                    <div class="update-meta">
                                        <i class="fas fa-clock"></i> <?php echo timeAgo($update['appika_updated_at']); ?>
                                        <br>
                                        <?php if (!empty($update['appika_update_source'])): ?>
                                            <small class="text-muted">
                                                <i class="fas fa-sync"></i> <?php echo ucfirst($update['appika_update_source']); ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <nav aria-label="Updates pagination">
                            <ul class="pagination">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?time=<?php echo $timeFilter; ?>&page=<?php echo $page - 1; ?>">Previous</a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?time=<?php echo $timeFilter; ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?time=<?php echo $timeFilter; ?>&page=<?php echo $page + 1; ?>">Next</a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <script src="../js/vendor.min.js"></script>
</body>
</html>
