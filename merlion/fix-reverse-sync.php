<?php
/**
 * Quick Fix for Reverse Sync Setup
 * This script adds the missing database columns needed for reverse sync
 */

session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

echo "<h2>🔧 Fix Appika Reverse Sync Setup</h2>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.check { color: green; font-weight: bold; }
.error { color: red; font-weight: bold; }
.warning { color: orange; font-weight: bold; }
.info { background: #e8f4fd; padding: 10px; border-radius: 5px; margin: 10px 0; }
.success { background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
.danger { background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
</style>";

// Step 1: Add missing columns
echo "<h3>Step 1: Adding Required Database Columns</h3>";

$columns = [
    "ALTER TABLE support_tickets ADD COLUMN IF NOT EXISTS last_appika_sync TIMESTAMP NULL DEFAULT NULL COMMENT 'Last time this ticket was synced from Appika'",
    "ALTER TABLE support_tickets ADD COLUMN IF NOT EXISTS appika_updated_at TIMESTAMP NULL DEFAULT NULL COMMENT 'When this ticket was last updated FROM Appika'",
    "ALTER TABLE support_tickets ADD COLUMN IF NOT EXISTS appika_update_source VARCHAR(50) NULL DEFAULT NULL COMMENT 'Source of last Appika update (admin_name, system, etc)'"
];

$success_count = 0;
foreach ($columns as $sql) {
    if (mysqli_query($conn, $sql)) {
        echo "<span class='check'>✅</span> Column added successfully<br>";
        $success_count++;
    } else {
        $error = mysqli_error($conn);
        if (strpos($error, 'Duplicate column name') !== false) {
            echo "<span class='check'>✅</span> Column already exists<br>";
            $success_count++;
        } else {
            echo "<span class='error'>❌</span> Error adding column: $error<br>";
        }
    }
}

// Step 2: Add indexes for better performance
echo "<h3>Step 2: Adding Database Indexes</h3>";

$indexes = [
    "CREATE INDEX IF NOT EXISTS idx_last_appika_sync ON support_tickets(last_appika_sync)",
    "CREATE INDEX IF NOT EXISTS idx_appika_id_sync ON support_tickets(appika_id, last_appika_sync)",
    "CREATE INDEX IF NOT EXISTS idx_appika_updated ON support_tickets(appika_updated_at)"
];

foreach ($indexes as $index) {
    if (mysqli_query($conn, $index)) {
        echo "<span class='check'>✅</span> Index created successfully<br>";
    } else {
        $error = mysqli_error($conn);
        if (strpos($error, 'Duplicate key name') !== false) {
            echo "<span class='check'>✅</span> Index already exists<br>";
        } else {
            echo "<span class='error'>❌</span> Error creating index: $error<br>";
        }
    }
}

// Step 3: Create logs directory
echo "<h3>Step 3: Creating Logs Directory</h3>";

$logsDir = '../logs';
if (!file_exists($logsDir)) {
    if (mkdir($logsDir, 0755, true)) {
        echo "<span class='check'>✅</span> Created logs directory<br>";
    } else {
        echo "<span class='error'>❌</span> Failed to create logs directory<br>";
    }
} else {
    echo "<span class='check'>✅</span> Logs directory already exists<br>";
}

// Step 4: Test the setup
echo "<h3>Step 4: Testing Reverse Sync Function</h3>";

if ($success_count >= 3) {
    require_once '../functions/appika_reverse_sync.php';
    
    // Find a ticket with Appika ID for testing
    $testQuery = "SELECT id, appika_id FROM support_tickets WHERE appika_id IS NOT NULL AND appika_id != '' LIMIT 1";
    $testResult = mysqli_query($conn, $testQuery);
    
    if ($testTicket = mysqli_fetch_assoc($testResult)) {
        echo "Testing with ticket ID: {$testTicket['id']} (Appika ID: {$testTicket['appika_id']})<br>";
        
        try {
            $syncResult = syncFromAppika($testTicket['id']);
            
            if ($syncResult['success']) {
                echo "<span class='check'>✅</span> Sync test successful: " . $syncResult['message'] . "<br>";
                if ($syncResult['updated']) {
                    echo "<span class='check'>🔄</span> Ticket was updated during test<br>";
                }
            } else {
                echo "<span class='error'>❌</span> Sync test failed: " . $syncResult['message'] . "<br>";
            }
        } catch (Exception $e) {
            echo "<span class='error'>❌</span> Error during sync test: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "<span class='warning'>⚠️</span> No tickets with Appika ID found for testing<br>";
        echo "<div class='info'>Create some tickets and sync them to Appika first, then test reverse sync.</div>";
    }
} else {
    echo "<span class='error'>❌</span> Cannot test - database setup incomplete<br>";
}

// Step 5: Summary
echo "<h3>✅ Setup Complete!</h3>";

if ($success_count >= 3) {
    echo "<div class='success'>";
    echo "<strong>🎉 Reverse Sync is now set up!</strong><br><br>";
    echo "<strong>How to test it:</strong><br>";
    echo "1. Go to Appika and update a ticket's status or priority<br>";
    echo "2. Use the test tool: <a href='test-reverse-sync.php'>test-reverse-sync.php</a><br>";
    echo "3. Or view a ticket: <a href='admin-ticket-detail.php'>admin-ticket-detail.php</a><br>";
    echo "4. Check the updates log: <a href='appika-updates-log.php'>appika-updates-log.php</a><br><br>";
    
    echo "<strong>What happens now:</strong><br>";
    echo "• When you view tickets, the system checks for Appika updates<br>";
    echo "• Updates from Appika are automatically applied to your database<br>";
    echo "• You'll see notifications when tickets are updated from Appika<br>";
    echo "</div>";
} else {
    echo "<div class='danger'>";
    echo "<strong>❌ Setup incomplete</strong><br>";
    echo "Some database columns could not be added. Check the errors above and try again.";
    echo "</div>";
}

echo "<h3>Next Steps</h3>";
echo "<div class='info'>";
echo "<strong>Testing Process:</strong><br>";
echo "1. <strong>In Appika:</strong> Update a ticket's status or priority<br>";
echo "2. <strong>In your system:</strong> Go to <a href='test-reverse-sync.php'>test-reverse-sync.php</a><br>";
echo "3. <strong>Click 'Test Single Sync'</strong> with the ticket ID<br>";
echo "4. <strong>Look for:</strong> 'Updated: Yes' and 'Changes: status, priority'<br><br>";

echo "<strong>Automatic Sync (Optional):</strong><br>";
echo "For automatic sync every 10 minutes, set up a cron job:<br>";
echo "<code>*/10 * * * * /usr/bin/php " . realpath('../merlion/sync-from-appika.php') . "</code><br>";
echo "</div>";
?>
